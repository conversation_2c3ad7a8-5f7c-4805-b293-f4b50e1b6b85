{"name": "@apollo/ui", "version": "1.0.0-beta.27", "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"import": "./src/index.ts", "require": "./dist/index.umd.cjs"}, "./legacy": {"types": "./dist/legacy/index.d.ts", "import": "./dist/legacy.js", "require": "./dist/legacy.umd.cjs"}, "./style.css": "./dist/index.css", "./_variables.css": "./dist/_variables.css"}, "bin": {"cj-apollo": "./dist/cli.cjs"}, "scripts": {"build:main": "TARGET_LIB=main vite build", "build:legacy": "TARGET_LIB=legacy vite build", "build:cli": "TARGET_LIB=cli vite build", "build": "tsc -b && pnpm run build:main && pnpm run build:legacy && pnpm run build:cli && pnpm run postbuild", "postbuild": "npx tsx src/_internal-scripts/generate-css-variables-file.ts", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "publish:code-connect": "npx figma connect publish"}, "publishConfig": {"@apollo:registry": "https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "dependencies": {"@apollo/token": "workspace:*", "@base-ui-components/react": "1.0.0-beta.0", "@design-systems/apollo-icons": "workspace:*", "@design-systems/apollo-ui": "1.13.9", "@mui/base": "5.0.0-beta.69", "axios": "^1.9.0", "class-variance-authority": "0.7.1", "classnames": "2.5.1", "date-fns": "2.30.0", "react-compiler-runtime": "19.0.0-beta-714736e-20250131", "react-datepicker": "8.3.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "9.17.0", "@figma/code-connect": "^1.3.1", "@modelcontextprotocol/sdk": "^1.12.1", "@testing-library/jest-dom": "^6.6.3", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@vitejs/plugin-react": "4.3.4", "@vitejs/plugin-react-swc": "3.5.0", "@vitest/browser": "^3.0.6", "babel-plugin-react-compiler": "19.0.0-beta-714736e-20250131", "chalk": "^5.4.1", "commander": "^14.0.0", "eslint": "9.17.0", "eslint-plugin-react-compiler": "19.0.0-beta-714736e-20250131", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-refresh": "0.4.16", "globals": "15.14.0", "ora": "^8.2.0", "playwright": "^1.50.1", "tsx": "^4.20.5", "typescript": "5.6.2", "typescript-eslint": "8.18.2", "vite": "6.0.5", "vite-plugin-dts": "4.5.0", "vitest": "^3.0.6", "vitest-browser-react": "^0.1.1"}, "publishedAt": "2025-02-20T03:14:38.440Z"}