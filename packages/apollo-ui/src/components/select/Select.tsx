import { type ReactElement } from "react"
import { Select as BaseSelect } from "@base-ui-components/react/select"
import classNames from "classnames"

import { ChevronIcon } from "../common"
import { Field } from "../field"
import { Input } from "../input"
import { MenuItem } from "../menu-item"
import { Portal } from "../portal"
import styles from "./select.module.css"
import type { OptionProps, SelectProps } from "./SelectProps"

export function Select<ValueType = string>({
  label,
  fullWidth,
  required,
  helperText,
  error,
  disabled,
  fieldProps,
  placeholder,
  children,
  ref,
  onChange,
  labelDecorator,
  helperTextDecorator,
  size = "medium",
  ...baseRootProps
}: SelectProps<ValueType>) {
  return (
    <BaseSelect.Root {...baseRootProps} onValueChange={onChange}>
      <Field
        {...fieldProps}
        label={label}
        required={required}
        helperText={helperText}
        error={error}
        fullWidth={fullWidth}
        disabled={disabled}
        labelDecorator={labelDecorator}
        helperTextDecorator={helperTextDecorator}
        className={classNames("ApolloSelect-fieldRoot", fieldProps?.className)}
      >
        <BaseSelect.Trigger
          className={classNames(
            "ApolloSelect-triggerContainer",
            styles.selectRoot,
            {
              [styles.selectRootFullWidth]: fullWidth,
            }
          )}
          ref={ref}
          render={(triggerProps) => (
            <div
              {...triggerProps}
              className={classNames(
                "ApolloSelect-trigger",
                triggerProps?.className
              )}
            >
              <BaseSelect.Value
                placeholder={placeholder}
                children={(_, value) =>
                  Array.isArray(children)
                    ? (children?.find(
                        (
                          child: ReactElement<{
                            label: string
                            value: ValueType
                          }>
                        ) => child.props?.value === value
                      )?.props?.label ?? null)
                    : null
                }
                render={(props) => (
                  <Input
                    className={classNames(
                      "ApolloSelect-input",
                      props?.className
                    )}
                    size={size}
                    error={error}
                    disabled={disabled}
                    value={(props?.children ?? "") as string}
                    readOnly
                    placeholder={placeholder}
                    endDecorator={<ChevronIcon />}
                  />
                )}
              />
            </div>
          )}
        />
      </Field>
      <Portal baseComponent={<BaseSelect.Portal />}>
        <BaseSelect.Positioner
          positionMethod="fixed"
          sticky
          alignItemWithTrigger={false}
          sideOffset={4}
          collisionAvoidance={{
            side: "shift",
            align: "shift",
          }}
          className={classNames(
            "ApolloSelect-positioner",
            styles.selectMenuPositioner
          )}
        >
          <BaseSelect.Popup
            className={classNames("ApolloSelect-popup", styles.selectMenuRoot)}
          >
            {children}
          </BaseSelect.Popup>
        </BaseSelect.Positioner>
      </Portal>
    </BaseSelect.Root>
  )
}

Select.Option = Option

export function Option<ValueType = string>({
  label,
  value,
  ...optionProps
}: OptionProps<ValueType>) {
  return (
    <BaseSelect.Item
      value={value}
      className={classNames("ApolloSelect-option", styles.selectMenuItem)}
      {...optionProps}
      render={(props, state) => (
        <MenuItem
          label={label}
          {...props}
          className={classNames("ApolloSelect-menuItem", props?.className)}
          selected={state.selected}
          disabled={state.disabled}
        />
      )}
    />
  )
}
