import type { ComponentProps } from "react"
import { use<PERSON><PERSON>back, useMemo } from "react"
import { Menu } from "@base-ui-components/react/menu"
import classNames from "classnames"

import { Portal } from "../../portal"
import styles from "../autocomplete.module.css"
import type {
  AutocompleteOption,
  MultipleAutocompleteProps,
} from "../AutocompleteProps"
import { useAutocomplete } from "../context/AutocompleteContext"
import { useDebounce } from "../hooks/useDebounce"
import { useLabelHeight } from "../hooks/useLabelHeight"
import { mergeOptions } from "../utils/mergeOptions"
import { MultipleAutocompleteMenuPopup } from "./MultipleAutocompleteMenuPopup"
import { ThumbnailAutocomplete } from "./ThumbnailAutocomplete"

const allowedKey = ["Escape", "ArrowUp", "ArrowDown", "ArrowRight", "ArrowLeft"]

export function MultipleAutocomplete<ValueType>({
  value = [],
  showSelectAll = false,
  hideCheckbox = false,
  selectAllText = "Select All",
  menuLabelText,
  onChange,
  onSearch,
}: Pick<
  MultipleAutocompleteProps<ValueType>,
  | "value"
  | "onChange"
  | "showSelectAll"
  | "selectAllText"
  | "onSearch"
  | "hideCheckbox"
  | "menuLabelText"
>) {
  const {
    options,
    search,
    setSearch,
    filteredOptions,
    size = "medium",
    debounceMs = 300,
    // New props
    className,
    style,
    id,
    name,
    autoFocus,
    tabIndex,
    "aria-label": ariaLabel,
    "aria-labelledby": ariaLabelledby,
    "aria-describedby": ariaDescribedby,
    inputProps,
    ...contextProps
  } = useAutocomplete<ValueType>()

  const { labelHeight, labelRef } = useLabelHeight(!!contextProps.label)

  const debouncedSearch = useDebounce((value: string) => {
    onSearch?.(value)
  }, debounceMs)

  const handleChange = useCallback(
    (
      changingOption: AutocompleteOption<ValueType>,
      event: React.MouseEvent<HTMLElement>
    ) => {
      const values = value
      const isSelected = values?.includes(changingOption.value)
      if (isSelected) {
        onChange?.(
          values?.filter((v) => v !== changingOption.value),
          event
        )
      } else {
        onChange?.([...(values ?? []), changingOption.value], event)
      }
    },
    [onChange, value]
  )

  const handleChipClose = useCallback(
    (option: AutocompleteOption<ValueType>) => (event: React.MouseEvent) => {
      event.stopPropagation()
      const values = value as ValueType[]
      onChange?.(
        values?.filter((v) => v !== option.value),
        event as React.MouseEvent<HTMLElement>
      )
    },
    [onChange, value]
  )

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      event.stopPropagation()
      const value = event.target.value
      setSearch(value)
      if (onSearch) {
        debouncedSearch(value)
      }
    },
    [onSearch, debouncedSearch, setSearch]
  )

  const handleClearAll = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      onChange?.([], event as React.MouseEvent<HTMLElement>)
    },
    [onChange]
  )

  const handleKeyboardEvents = useCallback(
    (props: ComponentProps<"div">) =>
      (event: React.KeyboardEvent<HTMLDivElement>) => {
        if (allowedKey.includes(event.code)) {
          if (event.type === "keydown") {
            props?.onKeyDown?.(event)
          } else if (event.type === "keyup") {
            props?.onKeyUp?.(event)
          }
        }
      },
    []
  )

  const handleOpenChange = useCallback(() => {
    setSearch("")
  }, [setSearch])

  const handleSelectAll = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation()
      const values = value as ValueType[]
      const availableOptions =
        filteredOptions.filter((opt) => !opt.disabled) ?? []

      // If all filtered options are selected, deselect all
      const isAllSelected = availableOptions.every((option) =>
        values?.includes(option.value)
      )

      if (isAllSelected) {
        // Remove all filtered options from selection
        const newValues = values?.filter(
          (v) => !availableOptions.find((opt) => opt.value === v)
        )
        onChange?.(newValues, event)
      } else {
        // Add all filtered options to selection
        const optionsToAdd = availableOptions
          .filter((opt) => !values?.includes(opt.value))
          .map((opt) => opt.value)
        onChange?.([...(values ?? []), ...optionsToAdd], event)
      }
    },
    [onChange, value, filteredOptions]
  )

  const isAllSelected = useMemo(
    () =>
      filteredOptions?.every((option) => value?.includes(option.value)) ??
      false,
    [filteredOptions, value]
  )

  // Selected options for display in chips (always show all selected)
  const selectedDisplayOptions = useMemo(
    () => mergeOptions(undefined, options, value),
    [options, value]
  )

  // Filtered options for dropdown menu (only show search results)
  const dropdownOptions = useMemo(() => {
    if (!search) return options ?? []
    return filteredOptions ?? []
  }, [search, options, filteredOptions])

  // Common props shared between components
  const commonProps = {
    size,
    value,
    fullWidth: contextProps.fullWidth,
    label: contextProps.label,
    helperText: contextProps.helperText,
    labelDecorator: contextProps.labelDecorator,
    helperTextDecorator: contextProps.helperTextDecorator,
    error: contextProps.error,
    disabled: contextProps.disabled,
    placeholder: contextProps.placeholder,
  }

  // Menu popup specific props
  const menuPopupProps = {
    loading: contextProps.loading,
    loadingMore: contextProps.loadingMore,
    loadMoreLabel: contextProps.loadMoreLabel,
    hasMore: contextProps.hasMore,
    loadingComponent: contextProps.loadingComponent,
    noOptionsComponent: contextProps.noOptionsComponent,
    disableSearch: contextProps.disableSearch,
    onLoadMore: contextProps.onLoadMore,
    multiple: true,
    search,
    showSelectAll,
    selectAllText,
    isAllSelected,
    hideCheckbox,
    menuLabelText,
  }

  const handleMenuStateChange = useCallback(
    (open: boolean, event?: Event) => {
      if (open) {
        contextProps?.onFocus?.(event)
      } else {
        contextProps?.onBlur?.(event)
      }
    },
    [contextProps]
  )

  const calculateSideOffset = useCallback(
    (data: { anchor: { height: number }; side: "top" | "bottom" }) =>
      -(data.anchor.height - (data.side === "bottom" ? labelHeight : 0)),
    [labelHeight]
  ) as Menu.Positioner.Props["sideOffset"]

  return (
    <Menu.Root
      closeParentOnEsc
      onOpenChange={handleMenuStateChange}
      onOpenChangeComplete={handleOpenChange}
    >
      <Menu.Trigger
        disabled={commonProps?.disabled}
        render={(props, state) => (
          <ThumbnailAutocomplete
            {...commonProps}
            {...props}
            open={state.open}
            options={selectedDisplayOptions}
            onChipClose={handleChipClose}
            labelRef={labelRef}
            onClearAll={handleClearAll}
            // New props
            className={className}
            style={style}
            id={id}
            name={name}
            autoFocus={autoFocus}
            tabIndex={tabIndex}
            aria-label={ariaLabel}
            aria-labelledby={ariaLabelledby}
            aria-describedby={ariaDescribedby}
            inputProps={inputProps}
          />
        )}
      />
      <Portal baseComponent={<Menu.Portal />}>
        <Menu.Positioner
          positionMethod="fixed"
          sideOffset={calculateSideOffset}
          className={classNames(
            "ApolloAutocomplete-Positioner",
            styles.autocompleteMenuPositioner
          )}
        >
          <MultipleAutocompleteMenuPopup
            {...commonProps}
            {...menuPopupProps}
            options={dropdownOptions}
            onChange={handleChange}
            onSearch={handleSearchChange}
            onChipClose={handleChipClose}
            onKeyboardEvent={handleKeyboardEvents}
            onClearAll={handleClearAll}
            onSelectAll={handleSelectAll}
          />
        </Menu.Positioner>
      </Portal>
    </Menu.Root>
  )
}
