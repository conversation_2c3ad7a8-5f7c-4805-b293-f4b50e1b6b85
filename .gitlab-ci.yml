image: node:20.18

variables:
  ROOT: ${CI_PROJECT_DIR}
  WWW_ROOT: ${CI_PROJECT_DIR}/apps/www
  PACKAGE_UI_ROOT: ${CI_PROJECT_DIR}/packages/ui
  PACKAGE_TOKENS_ROOT: ${CI_PROJECT_DIR}/packages/tokens
  PACKAGE_ICONS_ROOT: ${CI_PROJECT_DIR}/packages/icons

  PACKAGE_APOLLO_UI_ROOT: ${CI_PROJECT_DIR}/packages/apollo-ui
  PACKAGE_APOLLO_STORE_FRONT_ROOT: ${CI_PROJECT_DIR}/packages/apollo-storefront
  PACKAGE_APOLLO_TOKENS_ROOT: ${CI_PROJECT_DIR}/packages/apollo-token

cache:
  key:
    files:
      - pnpm-lock.yaml
  paths:
    - node_modules/
    - .pnpm-store/
    - apps/www/node_modules
    - packages/ui/node_modules
    - packages/icons/node_modules
    - packages/tokens/node_modules
    - tools/build-icons/node_modules
    - apps/docs/node_modules
    - packages/apollo-ui/node_modules
    - packages/apollo-token/node_modules

.pipeline:
  rules:
    - if: "$CI_COMMIT_BRANCH && $CI_COMMIT_BEFORE_SHA !~ /0{40}/"
      changes:
        - "*.md"
        - "*.example"
        - ".gitlab-ci.yml"
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG == null'
    - if: '$CI_PIPELINE_SOURCE == "web"'

.pipeline-test:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG == null'
      changes:
        - packages/**/*
        - tools/**/*
        - apps/**/*
        - scripts/**/*
        - ".gitlab-ci.yml"
        - "package.json"
    - if: '$CI_PIPELINE_SOURCE == "web"'

.pipeline-build:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG == null'
      changes:
        - packages/**/*
        - tools/**/*
        - apps/**/*
        - scripts/**/*
        - ".gitlab-ci.yml"
        - "package.json"

.pipeline-deploy:
  rules:
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG == null'
      changes:
        - packages/**/*
        - tools/**/*
        - apps/**/*
        - scripts/**/*
        - ".gitlab-ci.yml"

.pipeline-deploy-beta:
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^beta/ && $CI_PIPELINE_SOURCE == "push"'
      changes:
        - packages/**/*
        - tools/**/*
        - apps/**/*
        - scripts/**/*
        - ".gitlab-ci.yml"

.pipeline-publish:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH =~ /^beta/

default:
  before_script:
    - npm install -g corepack@latest
    - corepack enable
    - corepack prepare pnpm@10.15.1 --activate
    - pnpm config set store-dir .pnpm-store

stages:
  # - audit
  # - test
  # - build
  # - deploy
  - publish:packages
  - publish:document
  - publish:tag

# FYI: TEMPORARY DISABLED CAUSED NETLIFY CRASH
# audit:
#   extends:
#     - .pipeline
#   stage: audit
#   tags:
#     - cjexpress-internal
#   script:
#     - pnpm audit --fix

# TEMPORARY DISBLAED
# test-ui:
#   extends:
#     - .pipeline-test
#   stage: test
#   tags:
#     - cjexpress-internal
#   script:
#     - npm config set -- //${CI_SERVER_HOST}/api/v4/projects/${TOKENS_PROJECT_ID}/packages/npm/:_authToken=${AUTH_TOKEN_CI}
#     - pnpm install
#     - pnpm --filter=@design-systems/apollo-icons build
#     - pnpm run test:ui
#     - pnpm --filter=@design-systems/apollo-ui test-report
#   artifacts:
#     reports:
#       junit:
#         - $PACKAGE_UI_ROOT/junit.xml
#     expire_in: 1 day

# TEMPORARY DISBLAED
# test-www:
#   extends:
#     - .pipeline-test
#   stage: test
#   tags:
#     - cjexpress-internal
#   script:
#     - npm config set -- //${CI_SERVER_HOST}/api/v4/projects/${TOKENS_PROJECT_ID}/packages/npm/:_authToken=${AUTH_TOKEN_CI}
#     - pnpm install
#     - pnpm --filter=www test

deploy-docs:
  when: manual
  stage: publish:document
  extends:
    # Temporary beta doc
    - .pipeline-deploy-beta
  tags:
    - cjexpress-internal
  script:
    - echo "Installing Dependencies..."
    - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "@apollo:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">>.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${AUTH_TOKEN_CI}">>.npmrc
    - pnpm store prune
    - pnpm install
    - pnpm netlify deploy --filter docs --site "${NETLIFY_DOCS_SITE_ID}" --auth "${NETLIFY_ACCESS_TOKEN}" --prod --build

deploy-storybook-site:
  when: manual
  stage: publish:document
  # Deploy to gitlab Pages
  pages:
    publish: apps/apollo-docs/storybook-static
  extends:
    - .pipeline-deploy-beta
    - .pipeline-deploy
  tags:
    - cjexpress-internal
  script:
    - echo "Installing Dependencies..."
    - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "@apollo:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">>.npmrc
    - pnpm store prune
    - pnpm install
    - echo "Building dependencies..."
    - pnpm build:publish:icons
    - pnpm build:token-new
    - pnpm --filter=@apollo/storefront build
    - pnpm --filter=@apollo/ui build
    - echo "Building Storybook for Chromatic..."
    - pnpm --filter=apollo-docs build-storybook
    - echo "Deploying to Chromatic..."
    - pnpm --filter=apollo-docs publish:chromatic
    - echo "Building Storybook for GitLab Pages with base path..."
    # - export STORYBOOK_GITLAB_PAGES=true
    # - pnpm --filter=apollo-docs build-storybook:pages

publish-apollo-core:
  when: manual
  extends:
    - .pipeline-publish
  stage: publish:packages
  tags:
    - cjexpress-internal
  script:
    - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "@apollo:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">>.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">>.npmrc
    - pnpm store prune
    - pnpm install
    - echo "build publishing apollo-core 🚀"
    - pnpm build:publish-core
    - echo "Publishing apollo-core 🚀 component package..."
    - pnpm --filter=@apollo/core publish --dry-run --no-git-checks
    - pnpm --filter=@apollo/core publish --no-git-checks
  artifacts:
    paths:
      - $PACKAGE_APOLLO_UI_ROOT/dist

publish-apollo-ui:
  when: manual
  extends:
    - .pipeline-publish
  stage: publish:packages
  tags:
    - cjexpress-internal
  script:
    - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "@apollo:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">>.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">>.npmrc
    - pnpm store prune
    - pnpm install
    - echo "build publishing apollo token 🎨"
    - pnpm build:token-new
    - echo "build publishing apollo-ui 🚀"
    - pnpm build:publish-new
    - echo "Publishing apollo-ui 🚀 component package..."
    - pnpm --filter=@apollo/ui publish --dry-run --no-git-checks
    - pnpm --filter=@apollo/ui publish --no-git-checks
  artifacts:
    paths:
      - $PACKAGE_APOLLO_UI_ROOT/dist

publish-apollo-storefront:
  when: manual
  extends:
    - .pipeline-publish
  stage: publish:packages
  tags:
    - cjexpress-internal
  script:
    - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "@apollo:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">>.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">>.npmrc
    - pnpm store prune
    - pnpm install
    - echo "build publishing apollo token 🎨"
    - pnpm build:token-new
    - echo "build apollo-core 🎨"
    - pnpm --filter=@apollo/core build
    - echo "build apollo-ui 🎨"
    - pnpm --filter=@apollo/ui build
    - echo "build publishing apollo-storefront 🚀"
    - pnpm build:publish-storefront
    - echo "Publishing apollo-storefront 🚀 component package..."
    - pnpm --filter=@apollo/storefront publish --dry-run --no-git-checks
    - pnpm --filter=@apollo/storefront publish --no-git-checks
  artifacts:
    paths:
      - $PACKAGE_APOLLO_STORE_FRONT_ROOT/dist

publish-legacy-apollo-ui:
  when: manual
  extends:
    - .pipeline-publish
  stage: publish:packages
  tags:
    - cjexpress-internal
  script:
    - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "@apollo:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">>.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">>.npmrc
    - pnpm install
    - echo "build publishing apollo token 🎨"
    - pnpm build:token
    - echo "build publishing apollo-ui 🚀"
    - pnpm build:publish-legacy
    - echo "Publishing @design-systems/apollo-ui 🚀 component package..."
    - pnpm --filter=@design-systems/apollo-ui publish --dry-run --no-git-checks
    - pnpm --filter=@design-systems/apollo-ui publish --no-git-checks
  artifacts:
    paths:
      - $PACKAGE_APOLLO_UI_ROOT/dist

publish-apollo-token:
  when: manual
  extends:
    - .pipeline-publish
  stage: publish:packages
  tags:
    - cjexpress-internal
  script:
    - echo "@apollo:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">>.npmrc
    - pnpm install
    - echo "build publishing icons..."
    - pnpm build:publish:icons
    - echo "build publishing apollo token 🎨"
    - pnpm build:token-new
    - echo "Publishing apollo token component package 🎨"
    - pnpm --filter=@apollo/token publish --dry-run --no-git-checks
    - pnpm --filter=@apollo/token publish --no-git-checks
  artifacts:
    paths:
      - $PACKAGE_APOLLO_TOKENS_ROOT/dist

# publish-tokens:
#   when: manual
#   extends:
#     - .pipeline-publish
#   stage: publish:packages
#   tags:
#     - cjexpress-internal
#   script:
#     - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
#     - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">>.npmrc
#     - echo "build publishing ui..."
#     - pnpm build:tokens
#     - echo "Publishing ui component package..."
#     - pnpm --filter=@design-systems/tokens publish --dry-run --no-git-checks
#     - pnpm --filter=@design-systems/tokens publish --no-git-checks
#   artifacts:
#     paths:
#       - $PACKAGE_TOKENS_ROOT/dist

publish-icons:
  when: manual
  extends:
    - .pipeline-publish
  stage: publish:packages
  tags:
    - cjexpress-internal
  script:
    - echo "@design-systems:registry=http://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/">.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">>.npmrc
    - echo "build publishing icons..."
    - pnpm install
    - pnpm build:publish:icons
    - echo "Publishing icons component package..."
    - pnpm --filter=@design-systems/apollo-icons publish --dry-run --no-git-checks
    - pnpm --filter=@design-systems/apollo-icons publish --no-git-checks
  artifacts:
    paths:
      - $PACKAGE_ICONS_ROOT/dist

publish-tag:
  when: manual
  extends:
    - .pipeline-publish
  stage: publish:tag
  tags:
    - cjexpress-internal
  script:
    - echo "installing git..."
    - apt-get update && apt-get install -y git && rm -rf /var/lib/apt/lists/*
    - git remote show origin
    - git remote set-url origin ${CI_PROJECT_URL/gitlab.cjexpress.io/oauth2:${GITLAB_ACCESS_TOKEN}@gitlab.cjexpress.io}.git
    - git remote show origin
    - echo "git set config..."
    - git config user.email "${GITLAB_USER_EMAIL}"
    - git config user.name "${GITLAB_USER_NAME}"
    - echo "publishing tag..."
    - pnpm release:tag
